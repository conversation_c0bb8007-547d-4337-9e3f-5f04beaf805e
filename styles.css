/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Language Toggle */
.language-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 5px;
}

.lang-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #d4af37;
    color: #d4af37;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    font-size: 12px;
    transition: all 0.3s ease;
}

.lang-btn.active,
.lang-btn:hover {
    background: #d4af37;
    color: white;
}

/* Header */
.header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: white;
    padding: 20px 0;
    text-align: center;
}

.logo h1 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #d4af37;
    margin-bottom: 5px;
}

.logo p {
    font-size: 1.1rem;
    color: #ccc;
    font-weight: 300;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 80vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.description {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 30px;
    border-radius: 15px;
    margin: 40px 0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.description p {
    font-size: 1.1rem;
    line-height: 1.8;
}

/* Newsletter Form */
.newsletter-form {
    margin-top: 40px;
}

.form-group {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 10px;
    flex-wrap: wrap;
}

.form-group input {
    flex: 1;
    min-width: 250px;
    padding: 15px 20px;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
    background: rgba(255, 255, 255, 0.9);
}

.form-group button {
    background: #d4af37;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.form-group button:hover {
    background: #b8941f;
    transform: translateY(-2px);
}

/* Services Section */
.services {
    padding: 80px 0;
    background: #f8f9fa;
}

.services h3 {
    text-align: center;
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 50px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    max-width: 900px;
    margin: 0 auto;
}

.service-item {
    background: white;
    padding: 30px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.service-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.service-item i {
    font-size: 2.5rem;
    color: #d4af37;
    margin-bottom: 15px;
}

.service-item span {
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: white;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    max-width: 900px;
    margin: 0 auto;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.contact-item i {
    font-size: 1.8rem;
    color: #d4af37;
    margin-top: 5px;
}

.contact-item h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 8px;
    font-weight: 600;
}

.contact-item p {
    color: #666;
    line-height: 1.6;
}

.contact-item a {
    color: #d4af37;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-item a:hover {
    color: #b8941f;
}

/* Social Media Section */
.social-media {
    padding: 60px 0;
    background: #1a1a1a;
    text-align: center;
}

.social-media h3 {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    color: white;
    margin-bottom: 30px;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #d4af37;
    color: white;
    border-radius: 50%;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-links a:hover {
    background: #b8941f;
    transform: translateY(-3px);
}

/* Footer */
.footer {
    background: #2d2d2d;
    color: white;
    padding: 40px 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.footer-info h4 {
    font-family: 'Playfair Display', serif;
    color: #d4af37;
    margin-bottom: 10px;
}

.footer-info p {
    color: #ccc;
    margin-bottom: 5px;
}

.footer-copyright {
    border-top: 1px solid #444;
    padding-top: 20px;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .form-group {
        flex-direction: column;
        align-items: center;
    }
    
    .form-group input,
    .form-group button {
        width: 100%;
        max-width: 350px;
    }
    
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
    }
    
    .contact-info {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .social-links {
        flex-wrap: wrap;
    }
    
    .footer-content {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .logo h1 {
        font-size: 2rem;
    }
    
    .description {
        padding: 20px;
        margin: 20px 0;
    }
    
    .services h3 {
        font-size: 2rem;
    }
    
    .service-item {
        padding: 20px 15px;
    }
    
    .contact-item {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
}
